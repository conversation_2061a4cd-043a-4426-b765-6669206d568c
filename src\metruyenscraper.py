"""
Main MetruyenScraper Class
Orchestrates the entire scraping process with all components
"""

import asyncio
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from loguru import logger

from .config_manager import ConfigManager
from .scraper_engine import ScraperEngine
from .error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorType, ScrapingError
from .data_processor import DataProcessor


class MetruyenScraper:
    """Main scraper class that orchestrates all components"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialize the scraper with configuration"""
        self.config = ConfigManager(config_path)
        self.error_handler = ErrorHandler(self.config)
        self.data_processor = DataProcessor(self.config)
        self.scraper_engine: Optional[ScraperEngine] = None
        
        logger.info("MetruyenScraper initialized")
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def start(self) -> None:
        """Start the scraper engine"""
        self.scraper_engine = ScraperEngine(self.config.config_path)
        await self.scraper_engine.start()
        logger.info("MetruyenScraper started")
    
    async def close(self) -> None:
        """Close the scraper engine"""
        if self.scraper_engine:
            await self.scraper_engine.close()
        logger.info("MetruyenScraper closed")
    
    @property
    def retry_on_error(self):
        """Expose retry decorator"""
        return self.error_handler.retry_on_error
    
    async def scrape_url(self, url: str, target_name: str = "webtruyen") -> Optional[Dict[str, Any]]:
        """Scrape a single URL with error handling"""
        if not self.scraper_engine:
            raise RuntimeError("Scraper not started. Use async context manager.")
        
        @self.retry_on_error(retry_on=[ErrorType.NETWORK_ERROR, ErrorType.TIMEOUT_ERROR])
        async def _scrape_with_retry():
            return await self.scraper_engine.scrape_url(url, target_name)
        
        try:
            data = await _scrape_with_retry()
            self.data_processor.add_data(data)
            return data
        
        except ScrapingError as e:
            logger.error(f"Failed to scrape {url}: {e}")
            return None
    
    async def scrape_urls(self, urls: List[str], target_name: str = "webtruyen", 
                         max_concurrent: int = 3) -> List[Dict[str, Any]]:
        """Scrape multiple URLs concurrently"""
        if not urls:
            return []
        
        logger.info(f"Starting to scrape {len(urls)} URLs")
        
        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def scrape_with_semaphore(url: str) -> Optional[Dict[str, Any]]:
            async with semaphore:
                return await self.scrape_url(url, target_name)
        
        # Execute scraping tasks
        tasks = [scrape_with_semaphore(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter successful results
        successful_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Error scraping {urls[i]}: {result}")
            elif result is not None:
                successful_results.append(result)
        
        logger.info(f"Successfully scraped {len(successful_results)}/{len(urls)} URLs")
        return successful_results
    
    async def scrape_story_chapters(self, story_url: str, target_name: str = "webtruyen",
                                  max_chapters: Optional[int] = None) -> List[Dict[str, Any]]:
        """Scrape all chapters of a story from its table of contents page"""
        logger.info(f"📚 Starting to scrape story chapters from: {story_url}")

        # First, scrape the main story page to get chapter links
        logger.info("🔍 Extracting chapter list from story page...")
        story_data = await self.scraper_engine.scrape_chapters(story_url, target_name)
        if not story_data:
            logger.error(f"❌ Failed to scrape story page: {story_url}")
            return []

        # Extract chapter URLs from story_data
        chapter_urls = []
        chapters = story_data.get('chapters', [])
        logger.info(f"📋 Found {len(chapters)} chapters in story index")

        for i, chapter in enumerate(chapters):
            if isinstance(chapter, dict):
                chapter_url = chapter.get('url')
                chapter_title = chapter.get('title', f'Chapter {i+1}')
                if chapter_url:
                    chapter_urls.append(chapter_url)
                    logger.info(f"📄 Chapter {i+1}: {chapter_title[:50]}...")
                else:
                    logger.warning(f"⚠️ Chapter {i+1} missing URL: {chapter}")
            else:
                logger.warning(f"⚠️ Invalid chapter data format: {chapter}")

        # Limit chapters if specified
        if max_chapters and len(chapter_urls) > max_chapters:
            original_count = len(chapter_urls)
            chapter_urls = chapter_urls[:max_chapters]
            logger.info(f"📊 Limited to first {max_chapters} chapters (from {original_count} total)")

        if not chapter_urls:
            logger.warning("❌ No chapter URLs found in story index")
            return [story_data]

        logger.info(f"🚀 Starting to scrape {len(chapter_urls)} chapters...")

        # Scrape all chapters with progress tracking
        chapter_results = []
        for i, chapter_url in enumerate(chapter_urls):
            try:
                logger.info(f"📖 Scraping chapter {i+1}/{len(chapter_urls)}: {chapter_url}")
                chapter_data = await self.scrape_url(chapter_url, target_name)
                if chapter_data:
                    chapter_results.append(chapter_data)
                    logger.info(f"✅ Chapter {i+1} scraped successfully ({len(chapter_data.get('content', ''))} chars)")
                else:
                    logger.warning(f"⚠️ Chapter {i+1} returned no data")
            except Exception as e:
                logger.error(f"❌ Failed to scrape chapter {i+1}: {e}")
                continue

        logger.info(f"🎉 Successfully scraped {len(chapter_results)}/{len(chapter_urls)} chapters")

        # Combine story index data with chapter results
        all_results = [story_data] + chapter_results
        return all_results
    
    async def scrape_with_pagination(self, base_url: str, target_name: str = "webtruyen",
                                   max_pages: Optional[int] = None) -> List[Dict[str, Any]]:
        """Scrape content with pagination support"""
        logger.info(f"Starting pagination scraping from: {base_url}")
        
        all_results = []
        current_url = base_url
        page_count = 0
        
        while current_url and (max_pages is None or page_count < max_pages):
            page_count += 1
            logger.info(f"Scraping page {page_count}: {current_url}")
            
            page_data = await self.scrape_url(current_url, target_name)
            if not page_data:
                logger.warning(f"Failed to scrape page {page_count}")
                break
            
            all_results.append(page_data)
            
            # Look for next page URL
            navigation = page_data.get('navigation', {})
            current_url = navigation.get('next_chapter')  # or next_page depending on site
            
            if not current_url:
                logger.info("No more pages found")
                break
        
        logger.info(f"Completed pagination scraping: {len(all_results)} pages")
        return all_results
    
    def export_data(self, output_dir: str = "output") -> Dict[str, str]:
        """Export all scraped data"""
        return self.data_processor.export_data(output_dir)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive scraping statistics"""
        data_stats = self.data_processor.get_statistics()
        error_stats = self.error_handler.get_error_statistics()
        
        return {
            'data_statistics': data_stats,
            'error_statistics': error_stats,
            'failed_urls': self.error_handler.get_failed_urls()
        }
    
    def clear_data(self) -> None:
        """Clear all cached data and statistics"""
        self.data_processor.clear_cache()
        self.error_handler.clear_error_stats()
        logger.info("All data and statistics cleared")
    
    async def test_target_site(self, test_url: str, target_name: str = "webtruyen") -> Dict[str, Any]:
        """Test scraping capabilities on target site"""
        logger.info(f"Testing scraping on: {test_url}")
        
        test_results = {
            'url': test_url,
            'target': target_name,
            'success': False,
            'data_extracted': False,
            'content_locked': False,
            'navigation_found': False,
            'errors': []
        }
        
        try:
            data = await self.scrape_url(test_url, target_name)
            
            if data:
                test_results['success'] = True
                test_results['data_extracted'] = bool(data.get('content'))
                test_results['content_locked'] = data.get('is_locked', False)
                test_results['navigation_found'] = bool(data.get('navigation', {}).get('chapters'))
                
                logger.info("Test scraping successful")
            else:
                test_results['errors'].append("No data returned")
                logger.warning("Test scraping returned no data")
        
        except Exception as e:
            test_results['errors'].append(str(e))
            logger.error(f"Test scraping failed: {e}")
        
        return test_results
    
    def validate_configuration(self) -> Dict[str, Any]:
        """Validate current configuration"""
        validation_results = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        try:
            # Check required configuration sections
            required_sections = ['scraper', 'targets', 'output']
            for section in required_sections:
                if not self.config.get(section):
                    validation_results['errors'].append(f"Missing required section: {section}")
                    validation_results['valid'] = False
            
            # Check target configuration
            targets = self.config.get('targets', {})
            if not targets:
                validation_results['errors'].append("No target websites configured")
                validation_results['valid'] = False
            
            # Check browser configuration
            browser_config = self.config.get('scraper.browser', {})
            if not browser_config:
                validation_results['warnings'].append("No browser configuration found, using defaults")
            
            logger.info(f"Configuration validation: {'PASSED' if validation_results['valid'] else 'FAILED'}")
            
        except Exception as e:
            validation_results['errors'].append(f"Configuration validation error: {e}")
            validation_results['valid'] = False
        
        return validation_results
